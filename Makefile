# 高德地图POI获取工具 Makefile

.PHONY: help install test example clean setup

# 默认目标
help:
	@echo "高德地图POI获取工具"
	@echo "===================="
	@echo ""
	@echo "可用命令:"
	@echo "  setup     - 初始化项目环境"
	@echo "  install   - 安装依赖包"
	@echo "  test      - 运行测试"
	@echo "  example   - 运行示例"
	@echo "  clean     - 清理生成的文件"
	@echo "  nanjing   - 获取南京东路POI (需要API Key)"
	@echo ""
	@echo "使用前请先设置API Key:"
	@echo "  cp .env.example .env"
	@echo "  # 编辑 .env 文件，填入你的API Key"

# 初始化项目环境
setup:
	@echo "🚀 初始化项目环境..."
	@cp .env.example .env
	@echo "✅ 已创建 .env 文件，请编辑并填入你的API Key"
	@echo "📝 获取API Key: https://console.amap.com/dev/key/app"

# 安装依赖
install:
	@echo "📦 安装依赖包..."
	pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# 运行测试
test:
	@echo "🧪 运行测试..."
	python test_poi.py

# 运行示例
example:
	@echo "🎯 运行示例..."
	python example.py

# 获取南京东路POI
nanjing:
	@echo "🗺️  获取南京东路POI信息..."
	python poi_cli.py nanjing --types 餐饮服务 购物服务 生活服务 --output nanjing_road_pois.json --verbose

# 清理生成的文件
clean:
	@echo "🧹 清理生成的文件..."
	@rm -f *.json *.csv *.md
	@rm -rf __pycache__
	@rm -rf *.pyc
	@echo "✅ 清理完成"

# 显示项目状态
status:
	@echo "📊 项目状态:"
	@echo "============"
	@if [ -f .env ]; then echo "✅ .env 文件存在"; else echo "❌ .env 文件不存在"; fi
	@if [ -n "$$AMAP_API_KEY" ]; then echo "✅ API Key 已设置"; else echo "❌ API Key 未设置"; fi
	@echo ""
	@echo "📁 项目文件:"
	@ls -la *.py *.txt *.md 2>/dev/null || echo "无Python文件"
