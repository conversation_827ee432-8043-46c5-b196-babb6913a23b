"""
高德地图API配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 高德地图API配置
AMAP_API_KEY = os.getenv("AMAP_API_KEY", "")  # 从环境变量获取API Key
AMAP_BASE_URL = "https://restapi.amap.com/v3/place"

# 南京东路步行街区域坐标 (GCJ-02坐标系)
# 大致范围：从人民广场到外滩
NANJING_ROAD_AREA = {
    "center": {
        "longitude": 121.4825,  # 南京东路中心点经度
        "latitude": 31.2390     # 南京东路中心点纬度
    },
    # 多边形区域坐标 (矩形区域)
    "polygon": "121.4750,31.2430|121.4900,31.2430|121.4900,31.2350|121.4750,31.2350|121.4750,31.2430"
}

# POI类型编码 (可根据需要调整)
POI_TYPES = {
    "餐饮服务": "050000",
    "购物服务": "060000", 
    "生活服务": "070000",
    "体育休闲服务": "080000",
    "医疗保健服务": "090000",
    "住宿服务": "100000",
    "风景名胜": "110000",
    "商务住宅": "120000",
    "政府机构及社会团体": "130000",
    "科教文化服务": "140000",
    "交通设施服务": "150000",
    "金融保险服务": "160000",
    "公司企业": "170000",
    "道路附属设施": "180000",
    "地名地址信息": "190000",
    "公共设施": "200000"
}

# 默认搜索参数
DEFAULT_SEARCH_PARAMS = {
    "radius": 1000,        # 搜索半径(米)
    "page_size": 25,       # 每页返回数量
    "extensions": "all",   # 返回详细信息
    "rate_limit": 0.25     # 请求间隔(秒)，避免超过API限制
}

# 输出文件配置
OUTPUT_CONFIG = {
    "markdown_file": "nanjing_road_poi.md",
    "json_file": "nanjing_road_poi.json",
    "csv_file": "nanjing_road_poi.csv"
}
