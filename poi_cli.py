#!/usr/bin/env python3
"""
高德地图POI获取命令行工具
支持通过命令行参数进行各种POI搜索
"""
import argparse
import sys
import os
from poi_fetcher import AmapPOIFetcher
from config import POI_TYPES, NANJING_ROAD_AREA


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="高德地图POI信息获取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 获取南京东路附近的餐饮POI
  python poi_cli.py nanjing --types 餐饮服务 --output pois.json
  
  # 周边搜索
  python poi_cli.py around --lng 121.4825 --lat 31.2390 --radius 1000 --keywords 星巴克
  
  # 关键词搜索
  python poi_cli.py text --keywords 银行 --city 上海
  
  # 多边形搜索
  python poi_cli.py polygon --polygon "121.48,31.24|121.49,31.24|121.49,31.23|121.48,31.23|121.48,31.24"
  
  # 显示支持的POI类型
  python poi_cli.py types
        """
    )
    
    parser.add_argument(
        "--api-key", 
        help="高德地图API Key (也可通过环境变量AMAP_API_KEY设置)"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="输出文件名 (支持.json, .csv, .md格式)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="搜索命令")
    
    # 南京东路搜索
    nanjing_parser = subparsers.add_parser("nanjing", help="搜索南京东路附近POI")
    nanjing_parser.add_argument(
        "--types", 
        nargs="+",
        choices=list(POI_TYPES.keys()),
        default=["餐饮服务", "购物服务"],
        help="POI类型"
    )
    nanjing_parser.add_argument("--keywords", help="关键词")
    nanjing_parser.add_argument(
        "--use-circle", 
        action="store_true",
        help="使用圆形搜索而非多边形搜索"
    )
    
    # 周边搜索
    around_parser = subparsers.add_parser("around", help="周边搜索")
    around_parser.add_argument("--lng", type=float, required=True, help="中心点经度")
    around_parser.add_argument("--lat", type=float, required=True, help="中心点纬度")
    around_parser.add_argument("--radius", type=int, default=1000, help="搜索半径(米)")
    around_parser.add_argument("--keywords", help="关键词")
    around_parser.add_argument("--types", nargs="+", choices=list(POI_TYPES.keys()), help="POI类型")
    
    # 关键词搜索
    text_parser = subparsers.add_parser("text", help="关键词搜索")
    text_parser.add_argument("--keywords", required=True, help="搜索关键词")
    text_parser.add_argument("--city", default="上海", help="城市名称")
    text_parser.add_argument("--types", nargs="+", choices=list(POI_TYPES.keys()), help="POI类型")
    
    # 多边形搜索
    polygon_parser = subparsers.add_parser("polygon", help="多边形搜索")
    polygon_parser.add_argument("--polygon", required=True, help="多边形坐标串")
    polygon_parser.add_argument("--keywords", help="关键词")
    polygon_parser.add_argument("--types", nargs="+", choices=list(POI_TYPES.keys()), help="POI类型")
    
    # 显示POI类型
    subparsers.add_parser("types", help="显示支持的POI类型")
    
    return parser


def get_type_codes(type_names):
    """将类型名称转换为类型编码"""
    if not type_names:
        return ""
    
    codes = []
    for name in type_names:
        if name in POI_TYPES:
            codes.append(POI_TYPES[name])
    
    return "|".join(codes)


def save_results(pois, output_file, fetcher):
    """保存搜索结果"""
    if not output_file:
        return
    
    ext = os.path.splitext(output_file)[1].lower()
    
    if ext == ".json":
        fetcher.export_to_json(pois, output_file)
    elif ext == ".csv":
        fetcher.export_to_csv(pois, output_file)
    elif ext == ".md":
        fetcher.export_to_markdown(pois, output_file)
    else:
        print(f"⚠️  不支持的文件格式: {ext}")
        return
    
    print(f"✅ 结果已保存到: {output_file}")


def print_poi_summary(pois, verbose=False):
    """打印POI摘要信息"""
    if not pois:
        print("❌ 未找到任何POI")
        return
    
    print(f"✅ 找到 {len(pois)} 个POI")
    
    if verbose:
        print("\n📋 POI列表:")
        print("-" * 80)
        for i, poi in enumerate(pois, 1):
            print(f"{i:3d}. {poi['name']}")
            print(f"     地址: {poi['address']}")
            print(f"     类型: {poi['type']}")
            if poi['tel']:
                print(f"     电话: {poi['tel']}")
            if poi['rating']:
                print(f"     评分: {poi['rating']}")
            print()
    else:
        print("\n📋 前10个POI:")
        for i, poi in enumerate(pois[:10], 1):
            print(f"{i:2d}. {poi['name']} - {poi['address']}")


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 显示POI类型
    if args.command == "types":
        print("📋 支持的POI类型:")
        print("=" * 50)
        for name, code in POI_TYPES.items():
            print(f"{name:20s} {code}")
        return
    
    # 获取API Key
    api_key = args.api_key or os.getenv("AMAP_API_KEY")
    if not api_key:
        print("❌ 请设置API Key:")
        print("   方式1: 使用 --api-key 参数")
        print("   方式2: 设置环境变量 AMAP_API_KEY")
        print("   方式3: 在 .env 文件中设置 AMAP_API_KEY")
        sys.exit(1)
    
    # 创建POI获取器
    try:
        fetcher = AmapPOIFetcher(api_key)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)
    
    pois = []
    
    try:
        if args.command == "nanjing":
            # 南京东路搜索
            print(f"🔍 搜索南京东路附近的POI...")
            print(f"   类型: {', '.join(args.types)}")
            if args.keywords:
                print(f"   关键词: {args.keywords}")
            
            pois = fetcher.get_nanjing_road_pois(
                search_types=args.types,
                keywords=args.keywords or "",
                use_polygon=not args.use_circle
            )
        
        elif args.command == "around":
            # 周边搜索
            print(f"🔍 搜索坐标 ({args.lng}, {args.lat}) 周边 {args.radius}米内的POI...")
            if args.keywords:
                print(f"   关键词: {args.keywords}")
            if args.types:
                print(f"   类型: {', '.join(args.types)}")
            
            raw_pois = fetcher.search_around(
                longitude=args.lng,
                latitude=args.lat,
                radius=args.radius,
                keywords=args.keywords or "",
                types=get_type_codes(args.types)
            )
            pois = [fetcher.normalize_poi(poi) for poi in raw_pois]
        
        elif args.command == "text":
            # 关键词搜索
            print(f"🔍 在 {args.city} 搜索关键词: {args.keywords}")
            if args.types:
                print(f"   类型: {', '.join(args.types)}")
            
            raw_pois = fetcher.search_text(
                keywords=args.keywords,
                city=args.city,
                types=get_type_codes(args.types)
            )
            pois = [fetcher.normalize_poi(poi) for poi in raw_pois]
        
        elif args.command == "polygon":
            # 多边形搜索
            print(f"🔍 在指定多边形区域内搜索POI...")
            if args.keywords:
                print(f"   关键词: {args.keywords}")
            if args.types:
                print(f"   类型: {', '.join(args.types)}")
            
            raw_pois = fetcher.search_polygon(
                polygon=args.polygon,
                keywords=args.keywords or "",
                types=get_type_codes(args.types)
            )
            pois = [fetcher.normalize_poi(poi) for poi in raw_pois]
        
        # 显示结果
        print_poi_summary(pois, args.verbose)
        
        # 保存结果
        if args.output:
            save_results(pois, args.output, fetcher)
    
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
