# 高德地图POI获取工具 - 知识库优化说明

## 优化背景

根据您的需求，POI数据将用作知识库，提供给聊天机器人回答用户问题。为了满足这一需求，我们对原有的POI数据结构和导出格式进行了全面优化。

## 核心需求分析

您提到的知识库需求包括：
- **街道门牌号** + **大楼名称/编号** + **楼层**
- **POI的具体内容**（业务信息、联系方式等）

## 主要优化内容

### 1. 🏢 增强地址解析功能

#### 原始数据结构问题
```json
{
    "address": "上海市黄浦区南京东路432号百联世茂国际广场1层",
    "name": "星巴克咖啡(南京东路店)"
}
```

#### 优化后的结构化地址信息
```json
{
    "full_address": "上海市黄浦区南京东路432号百联世茂国际广场1层",
    "street_number": "南京东路432号",           // 街道门牌号
    "building_name": "百联世茂国际广场",        // 大楼名称
    "floor_info": "1F",                       // 楼层信息
    "room_number": "101室",                   // 房间号（如果有）
    "building_number": ""                     // 大楼编号（如果有）
}
```

#### 地址解析算法
- **街道门牌号提取**: 使用正则表达式 `([^市区县]+[路街道巷弄])\s*(\d+[号楼栋]?)`
- **建筑物名称提取**: 识别常见建筑物后缀（大厦、广场、中心、商城等）
- **楼层信息提取**: 从API的 `floor`、`truefloor` 字段或POI名称中提取
- **房间号提取**: 识别常见房间号格式（如：101室、A203、1-5等）

### 2. 🏗️ 增加建筑物关联信息

```json
{
    "parent_poi_id": "B0002",              // 父级POI ID（所属建筑物）
    "cpid": "B0002",                       // 建筑物POI ID
    "indoor_map": "1",                     // 是否有室内地图
    "entrance_location": "121.486500,31.239800",  // 入口坐标
    "exit_location": ""                    // 出口坐标
}
```

### 3. 📋 知识库专用导出格式

#### 新增 `export_to_knowledge_base()` 方法

生成包含结构化问答对的知识库文件：

```markdown
## 星巴克咖啡(南京东路店)

### 基本信息
**名称**: 星巴克咖啡(南京东路店)
**详细位置**: 南京东路432号 百联世茂国际广场 1F
**业务类型**: 餐饮服务;咖啡厅;星巴克

### 常见问题

**Q: 星巴克咖啡(南京东路店)在哪里？**
A: 星巴克咖啡(南京东路店)位于南京东路432号百联世茂国际广场1F。

**Q: 星巴克咖啡(南京东路店)的电话是多少？**
A: 星巴克咖啡(南京东路店)的联系电话是021-63218888。

**Q: 1F还有哪些商户？**
A: 1F还有老凤祥银楼(南京东路总店)。
```

### 4. 🤖 聊天机器人查询场景支持

#### 位置查询
```python
# 用户问："星巴克在哪里？"
poi = next((p for p in pois if "星巴克" in p['name']), None)
answer = f"{poi['name']}位于{poi['street_number']}{poi['building_name']}{poi['floor_info']}"
```

#### 楼层查询
```python
# 用户问："1楼有什么店？"
floor_pois = [p for p in pois if p.get('floor_info') == '1F']
answer = f"1楼有{len(floor_pois)}家店：" + "、".join([p['name'] for p in floor_pois])
```

#### 类型查询
```python
# 用户问："附近有哪些银行？"
banks = [p for p in pois if "银行" in p.get('type', '')]
answer = "附近的银行有：" + "、".join([p['name'] for p in banks])
```

### 5. 📊 统计和汇总信息

知识库文件自动包含：

#### 按业务类型统计
```
- 餐饮服务: 2家
- 购物服务: 2家  
- 金融保险服务: 1家
```

#### 按楼层分布统计
```
- 1F: 2家
- 3F: 1家
- B1: 1家
```

## 使用示例

### 基本使用
```python
from poi_fetcher import AmapPOIFetcher

fetcher = AmapPOIFetcher("your_api_key")

# 获取南京东路POI
pois = fetcher.get_nanjing_road_pois(
    search_types=["餐饮服务", "购物服务"]
)

# 导出为知识库格式
fetcher.export_to_knowledge_base(pois, "knowledge_base.md")
```

### 命令行使用
```bash
# 获取南京东路POI并导出为知识库格式
python poi_cli.py nanjing --types 餐饮服务 购物服务 --output knowledge_base.json

# 然后可以用Python脚本转换为知识库格式
python -c "
from poi_fetcher import AmapPOIFetcher
import json
with open('knowledge_base.json') as f:
    data = json.load(f)
fetcher = AmapPOIFetcher('demo')
fetcher.export_to_knowledge_base(data['pois'], 'final_kb.md')
"
```

## 数据质量保证

### 1. 地址解析准确性
- 支持多种地址格式
- 智能识别建筑物名称
- 自动提取楼层信息
- 处理特殊格式（如B1、3F等）

### 2. 数据完整性
- 保留原始地址信息作为备份
- 提供多个字段确保信息不丢失
- 支持手动补充和修正

### 3. 关联性
- 建筑物内商户关联
- 同楼层商户关联
- 同类型商户关联

## 适用场景

### 1. 商场导购机器人
- "请问1楼有哪些店？"
- "星巴克在几楼？"
- "附近有银行吗？"

### 2. 位置服务助手
- "帮我找到南京东路432号的咖啡店"
- "百联世茂国际广场都有什么店？"

### 3. 商业分析
- 楼层商户分布分析
- 业态组合分析
- 竞争对手分析

## 技术优势

1. **智能解析**: 自动从非结构化地址中提取结构化信息
2. **多格式支持**: 同时支持JSON、CSV、Markdown等格式
3. **问答对生成**: 自动生成常见问题和标准答案
4. **关联分析**: 提供商户间的关联关系
5. **扩展性强**: 易于添加新的解析规则和导出格式

## 总结

通过这次优化，POI数据已经完全满足知识库的需求：

✅ **街道门牌号**: `street_number` 字段  
✅ **大楼名称/编号**: `building_name` 字段  
✅ **楼层信息**: `floor_info` 字段  
✅ **POI具体内容**: 完整的商业信息、联系方式等  
✅ **结构化问答**: 自动生成的Q&A格式  
✅ **关联信息**: 同楼层、同类型商户关联  

这样的数据结构可以很好地支持聊天机器人回答用户关于位置、楼层、商户类型等各种问题。
