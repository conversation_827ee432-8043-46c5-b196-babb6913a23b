"""
高德地图POI获取工具演示
展示工具的功能和用法，无需真实API Key
"""
import json
from poi_fetcher import AmapPOIFetcher
from config import POI_TYPES, NANJING_ROAD_AREA


def demo_config():
    """演示配置信息"""
    print("🔧 配置信息演示")
    print("=" * 50)
    
    print(f"📍 南京东路中心坐标: ({NANJING_ROAD_AREA['center']['longitude']}, {NANJING_ROAD_AREA['center']['latitude']})")
    print(f"📐 搜索区域: {NANJING_ROAD_AREA['polygon']}")
    
    print(f"\n📋 支持的POI类型 (共{len(POI_TYPES)}种):")
    for i, (name, code) in enumerate(POI_TYPES.items(), 1):
        print(f"  {i:2d}. {name:15s} - {code}")
        if i >= 10:  # 只显示前10个
            print(f"     ... 还有 {len(POI_TYPES) - 10} 种类型")
            break


def demo_data_structure():
    """演示数据结构"""
    print("\n📊 数据结构演示")
    print("=" * 50)
    
    # 模拟POI数据
    sample_poi = {
        "id": "B0FFFAB6J2",
        "name": "星巴克咖啡(南京东路店)",
        "type": "餐饮服务;咖啡厅;星巴克",
        "typecode": "050301",
        "address": "上海市黄浦区南京东路432号",
        "location": "121.486532,31.239812",
        "tel": "021-63218888",
        "website": "https://www.starbucks.com.cn",
        "pname": "上海市",
        "cityname": "上海市",
        "adname": "黄浦区",
        "business_area": "南京东路",
        "rating": "4.6",
        "cost": "45",
        "tag": "咖啡,饮品,轻食,WiFi",
        "distance": "50"
    }
    
    print("🏪 POI数据示例:")
    print(json.dumps(sample_poi, ensure_ascii=False, indent=2))
    
    # 演示数据标准化
    print("\n🔄 数据标准化后:")
    normalized = AmapPOIFetcher.normalize_poi(sample_poi)
    print(json.dumps(normalized, ensure_ascii=False, indent=2))


def demo_search_methods():
    """演示搜索方法"""
    print("\n🔍 搜索方法演示")
    print("=" * 50)
    
    print("1️⃣ 关键词搜索:")
    print("   fetcher.search_text('星巴克', '上海')")
    print("   → 在上海市搜索所有星巴克门店")
    
    print("\n2️⃣ 周边搜索:")
    print("   fetcher.search_around(121.4825, 31.2390, 1000, '银行')")
    print("   → 搜索指定坐标1km范围内的银行")
    
    print("\n3️⃣ 多边形搜索:")
    print("   fetcher.search_polygon(polygon_coords, types='050000')")
    print("   → 在指定多边形区域内搜索餐饮服务")
    
    print("\n4️⃣ 南京东路专用搜索:")
    print("   fetcher.get_nanjing_road_pois(['餐饮服务', '购物服务'])")
    print("   → 获取南京东路步行街的餐饮和购物POI")


def demo_export_formats():
    """演示导出格式"""
    print("\n📤 导出格式演示")
    print("=" * 50)
    
    # 模拟POI数据
    sample_pois = [
        {
            "id": "B0001",
            "name": "老凤祥银楼",
            "type": "购物服务;珠宝饰品店",
            "address": "上海市黄浦区南京东路432号",
            "location": "121.486532,31.239812",
            "tel": "021-63218888",
            "rating": "4.5",
            "cost": "500",
            "tag": "黄金,珠宝,老字号",
            "typecode": "060201",
            "website": "",
            "email": "",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "postcode": "",
            "distance": ""
        },
        {
            "id": "B0002",
            "name": "第一食品商店",
            "type": "购物服务;超市",
            "address": "上海市黄浦区南京东路720号",
            "location": "121.485123,31.240456",
            "tel": "021-63222333",
            "rating": "4.2",
            "cost": "80",
            "tag": "特产,零食,老字号",
            "typecode": "060101",
            "website": "",
            "email": "",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "postcode": "",
            "distance": ""
        }
    ]
    
    # 创建临时fetcher用于演示
    fetcher = AmapPOIFetcher("demo_key")
    
    print("📄 Markdown格式预览:")
    print("-" * 30)
    md_content = fetcher.export_to_markdown(sample_pois, "demo_output.md")
    print(md_content[:300] + "..." if len(md_content) > 300 else md_content)
    
    print("\n📊 JSON格式:")
    fetcher.export_to_json(sample_pois, "demo_output.json")
    print("   已生成 demo_output.json")
    
    print("\n📈 CSV格式:")
    fetcher.export_to_csv(sample_pois, "demo_output.csv")
    print("   已生成 demo_output.csv")
    
    print(f"\n✅ 共导出 {len(sample_pois)} 个POI到3种格式")


def demo_cli_usage():
    """演示命令行用法"""
    print("\n💻 命令行工具用法")
    print("=" * 50)
    
    commands = [
        ("显示POI类型", "python poi_cli.py types"),
        ("南京东路搜索", "python poi_cli.py nanjing --types 餐饮服务 购物服务 --output result.json"),
        ("周边搜索", "python poi_cli.py around --lng 121.4825 --lat 31.2390 --radius 1000 --keywords 银行"),
        ("关键词搜索", "python poi_cli.py text --keywords 星巴克 --city 上海 --output starbucks.csv"),
        ("多边形搜索", "python poi_cli.py polygon --polygon '121.48,31.24|121.49,31.24|121.49,31.23|121.48,31.23|121.48,31.24'")
    ]
    
    for desc, cmd in commands:
        print(f"🔸 {desc}:")
        print(f"   {cmd}")
        print()


def main():
    """主演示函数"""
    print("🎯 高德地图POI获取工具功能演示")
    print("=" * 60)
    
    # 运行各个演示
    demo_config()
    demo_data_structure()
    demo_search_methods()
    demo_export_formats()
    demo_cli_usage()
    
    print("\n🎉 演示完成！")
    print("\n📝 下一步:")
    print("   1. 申请高德地图API Key: https://console.amap.com/dev/key/app")
    print("   2. 设置环境变量: export AMAP_API_KEY=your_key")
    print("   3. 运行真实示例: python example.py")
    print("   4. 使用命令行工具: python poi_cli.py --help")


if __name__ == "__main__":
    main()
