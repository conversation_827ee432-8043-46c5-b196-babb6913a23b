"""
知识库优化演示
展示优化后的POI数据结构和知识库导出功能
"""
import json
from poi_fetcher import AmapPOIFetcher


def create_sample_pois():
    """创建示例POI数据，包含详细的地址和楼层信息"""
    sample_pois = [
        {
            "id": "B0001",
            "name": "星巴克咖啡(南京东路店)",
            "type": "餐饮服务;咖啡厅;星巴克",
            "typecode": "050301",
            "address": "上海市黄浦区南京东路432号百联世茂国际广场1层",
            "location": "121.486532,31.239812",
            "tel": "021-63218888",
            "website": "https://www.starbucks.com.cn",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "floor": "1",
            "truefloor": "1F",
            "parent": "B0002",
            "cpid": "B0002",
            "indoor_map": "1",
            "entr_location": "121.486500,31.239800",
            "biz_ext": {
                "rating": "4.6",
                "cost": "45"
            },
            "tag": "咖啡,饮品,轻食,WiFi",
            "distance": ""
        },
        {
            "id": "B0002",
            "name": "百联世茂国际广场",
            "type": "购物服务;购物中心;综合商场",
            "typecode": "060400",
            "address": "上海市黄浦区南京东路432号",
            "location": "121.486532,31.239812",
            "tel": "021-63210000",
            "website": "http://www.shimaogroup.com",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "floor": "",
            "truefloor": "",
            "parent": "",
            "cpid": "B0002",
            "indoor_map": "1",
            "entr_location": "121.486500,31.239800",
            "biz_ext": {
                "rating": "4.3",
                "cost": "200"
            },
            "tag": "购物,餐饮,娱乐,停车",
            "distance": ""
        },
        {
            "id": "B0003",
            "name": "中国银行(南京东路支行)",
            "type": "金融保险服务;银行;中国银行",
            "typecode": "160101",
            "address": "上海市黄浦区南京东路432号百联世茂国际广场B1层",
            "location": "121.486532,31.239812",
            "tel": "021-63218899",
            "website": "http://www.boc.cn",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "floor": "-1",
            "truefloor": "B1",
            "parent": "B0002",
            "cpid": "B0002",
            "indoor_map": "1",
            "entr_location": "121.486500,31.239800",
            "biz_ext": {
                "rating": "4.2",
                "cost": ""
            },
            "tag": "ATM,理财,外汇,信用卡",
            "distance": ""
        },
        {
            "id": "B0004",
            "name": "老凤祥银楼(南京东路总店)",
            "type": "购物服务;珠宝饰品店;老凤祥",
            "typecode": "060201",
            "address": "上海市黄浦区南京东路432号1楼101室",
            "location": "121.486532,31.239812",
            "tel": "021-63218877",
            "website": "http://www.laofengxiang.com",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "floor": "1",
            "truefloor": "1F",
            "parent": "B0002",
            "cpid": "B0002",
            "indoor_map": "1",
            "entr_location": "121.486500,31.239800",
            "biz_ext": {
                "rating": "4.5",
                "cost": "500"
            },
            "tag": "黄金,珠宝,钻石,老字号",
            "distance": ""
        },
        {
            "id": "B0005",
            "name": "海底捞火锅(南京东路店)",
            "type": "餐饮服务;火锅店;海底捞",
            "typecode": "050201",
            "address": "上海市黄浦区南京东路432号百联世茂国际广场3层301-305室",
            "location": "121.486532,31.239812",
            "tel": "021-63218866",
            "website": "http://www.haidilao.com",
            "pname": "上海市",
            "cityname": "上海市",
            "adname": "黄浦区",
            "business_area": "南京东路",
            "floor": "3",
            "truefloor": "3F",
            "parent": "B0002",
            "cpid": "B0002",
            "indoor_map": "1",
            "entr_location": "121.486500,31.239800",
            "biz_ext": {
                "rating": "4.7",
                "cost": "120"
            },
            "tag": "火锅,川菜,服务好,排队",
            "distance": ""
        }
    ]
    
    return sample_pois


def demo_address_parsing():
    """演示地址解析功能"""
    print("🔍 地址解析功能演示")
    print("=" * 50)
    
    sample_pois = create_sample_pois()
    
    for poi in sample_pois:
        print(f"\n📍 {poi['name']}")
        print(f"原始地址: {poi['address']}")
        
        # 标准化POI数据
        normalized = AmapPOIFetcher.normalize_poi(poi)
        
        print("解析结果:")
        if normalized['street_number']:
            print(f"  街道门牌: {normalized['street_number']}")
        if normalized['building_name']:
            print(f"  建筑物名称: {normalized['building_name']}")
        if normalized['floor_info']:
            print(f"  楼层信息: {normalized['floor_info']}")
        if normalized['room_number']:
            print(f"  房间号: {normalized['room_number']}")
        
        print("-" * 30)


def demo_knowledge_base_export():
    """演示知识库导出功能"""
    print("\n📚 知识库导出功能演示")
    print("=" * 50)
    
    sample_pois = create_sample_pois()
    
    # 标准化所有POI数据
    normalized_pois = [AmapPOIFetcher.normalize_poi(poi) for poi in sample_pois]
    
    # 创建临时fetcher用于演示
    fetcher = AmapPOIFetcher("demo_key")
    
    # 导出为知识库格式
    print("正在生成知识库...")
    kb_content = fetcher.export_to_knowledge_base(normalized_pois, "demo_knowledge_base.md")
    
    print("✅ 知识库已生成: demo_knowledge_base.md")
    
    # 显示部分内容预览
    print("\n📄 内容预览:")
    print("-" * 30)
    lines = kb_content.split('\n')
    for i, line in enumerate(lines[:30]):  # 显示前30行
        print(line)
    
    if len(lines) > 30:
        print("...")
        print(f"(还有 {len(lines) - 30} 行内容)")


def demo_structured_data():
    """演示结构化数据格式"""
    print("\n📊 结构化数据格式演示")
    print("=" * 50)
    
    sample_pois = create_sample_pois()
    
    # 标准化第一个POI作为示例
    normalized = AmapPOIFetcher.normalize_poi(sample_pois[0])
    
    print("🏪 标准化后的POI数据结构:")
    print(json.dumps(normalized, ensure_ascii=False, indent=2))


def demo_chatbot_scenarios():
    """演示聊天机器人应用场景"""
    print("\n🤖 聊天机器人应用场景演示")
    print("=" * 50)
    
    sample_pois = create_sample_pois()
    normalized_pois = [AmapPOIFetcher.normalize_poi(poi) for poi in sample_pois]
    
    # 模拟用户查询场景
    scenarios = [
        {
            "query": "星巴克在哪里？",
            "answer_type": "位置查询",
            "poi_name": "星巴克咖啡(南京东路店)"
        },
        {
            "query": "百联世茂国际广场1楼有什么店？",
            "answer_type": "楼层查询",
            "floor": "1F"
        },
        {
            "query": "南京东路432号有哪些银行？",
            "answer_type": "类型查询",
            "poi_type": "金融保险服务"
        },
        {
            "query": "海底捞的电话是多少？",
            "answer_type": "联系方式查询",
            "poi_name": "海底捞火锅(南京东路店)"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n❓ 用户问题: {scenario['query']}")
        print(f"📋 查询类型: {scenario['answer_type']}")
        
        # 根据查询类型生成答案
        if scenario['answer_type'] == "位置查询":
            poi = next((p for p in normalized_pois if scenario['poi_name'] in p['name']), None)
            if poi:
                answer = f"{poi['name']}位于{poi['street_number']}{poi['building_name']}"
                if poi['floor_info']:
                    answer += f"{poi['floor_info']}"
                answer += "。"
                print(f"🤖 机器人回答: {answer}")
        
        elif scenario['answer_type'] == "楼层查询":
            floor_pois = [p for p in normalized_pois if p.get('floor_info') == scenario['floor']]
            if floor_pois:
                names = [p['name'] for p in floor_pois]
                answer = f"百联世茂国际广场1楼有{len(names)}家店铺：" + "、".join(names) + "。"
                print(f"🤖 机器人回答: {answer}")
        
        elif scenario['answer_type'] == "类型查询":
            type_pois = [p for p in normalized_pois if scenario['poi_type'] in p.get('type', '')]
            if type_pois:
                names = [p['name'] for p in type_pois]
                answer = f"南京东路432号的银行有：" + "、".join(names) + "。"
                print(f"🤖 机器人回答: {answer}")
        
        elif scenario['answer_type'] == "联系方式查询":
            poi = next((p for p in normalized_pois if scenario['poi_name'] in p['name']), None)
            if poi and poi.get('tel'):
                answer = f"{poi['name']}的联系电话是{poi['tel']}。"
                print(f"🤖 机器人回答: {answer}")
        
        print("-" * 40)


def main():
    """主演示函数"""
    print("🎯 知识库优化功能演示")
    print("=" * 60)
    
    # 运行各个演示
    demo_address_parsing()
    demo_structured_data()
    demo_knowledge_base_export()
    demo_chatbot_scenarios()
    
    print("\n🎉 演示完成！")
    print("\n📝 优化总结:")
    print("✅ 增强了地址解析功能，提取街道门牌号、建筑物名称、楼层信息")
    print("✅ 优化了数据结构，包含更多知识库所需的字段")
    print("✅ 新增知识库专用导出格式，包含结构化问答对")
    print("✅ 支持多种聊天机器人查询场景")
    print("✅ 提供楼层分布和商户关联信息")


if __name__ == "__main__":
    main()
