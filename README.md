# 高德地图POI信息获取工具

这是一个基于高德地图Web服务API的POI（Point of Interest）信息获取工具，可以灵活地获取指定区域内的各种兴趣点信息。

## 功能特性

- 🗺️ **多种搜索方式**: 支持关键词搜索、周边搜索、多边形搜索
- 📍 **灵活的区域设置**: 可以自定义搜索范围和区域形状
- 🏪 **丰富的POI类型**: 支持餐饮、购物、生活服务等20+种POI类型
- 📊 **多格式导出**: 支持导出为Markdown、JSON、CSV格式
- ⚡ **智能限流**: 自动控制请求频率，避免超出API限制
- 🎯 **预设区域**: 内置南京东路步行街区域配置

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 申请高德地图API Key

1. 访问 [高德开放平台](https://console.amap.com/dev/key/app)
2. 注册并创建应用
3. 申请"Web服务API"类型的Key

### 3. 配置API Key

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的API Key：
```
AMAP_API_KEY=your_actual_api_key_here
```

### 4. 运行示例

```bash
python example.py
```

## 使用方法

### 基本用法

```python
from poi_fetcher import AmapPOIFetcher

# 创建POI获取器
fetcher = AmapPOIFetcher("your_api_key")

# 获取南京东路附近的餐饮和购物POI
pois = fetcher.get_nanjing_road_pois(
    search_types=["餐饮服务", "购物服务"]
)

# 导出数据
fetcher.export_to_markdown(pois, "pois.md")
fetcher.export_to_json(pois, "pois.json")
fetcher.export_to_csv(pois, "pois.csv")
```

### 周边搜索

```python
# 搜索指定坐标周边1km内的银行
pois = fetcher.search_around(
    longitude=121.4825,
    latitude=31.2390,
    radius=1000,
    types="160000"  # 金融保险服务
)
```

### 关键词搜索

```python
# 在上海搜索星巴克
pois = fetcher.search_text(
    keywords="星巴克",
    city="上海"
)
```

### 多边形搜索

```python
# 在自定义多边形区域内搜索
polygon = "121.4800,31.2400|121.4850,31.2400|121.4850,31.2380|121.4800,31.2380|121.4800,31.2400"
pois = fetcher.search_polygon(
    polygon=polygon,
    types="050000"  # 餐饮服务
)
```

## 支持的POI类型

| 类型名称 | 类型编码 | 说明 |
|---------|---------|------|
| 餐饮服务 | 050000 | 餐厅、咖啡厅、酒吧等 |
| 购物服务 | 060000 | 商场、超市、专卖店等 |
| 生活服务 | 070000 | 美容美发、洗衣店等 |
| 体育休闲服务 | 080000 | 健身房、KTV、电影院等 |
| 医疗保健服务 | 090000 | 医院、药店、诊所等 |
| 住宿服务 | 100000 | 酒店、宾馆、民宿等 |
| 风景名胜 | 110000 | 景点、公园、博物馆等 |
| 商务住宅 | 120000 | 写字楼、住宅小区等 |
| 政府机构及社会团体 | 130000 | 政府部门、社会组织等 |
| 科教文化服务 | 140000 | 学校、图书馆、培训机构等 |
| 交通设施服务 | 150000 | 地铁站、公交站、停车场等 |
| 金融保险服务 | 160000 | 银行、ATM、保险公司等 |
| 公司企业 | 170000 | 各类企业、工厂等 |

## 配置说明

### 区域配置

在 `config.py` 中可以修改搜索区域：

```python
NANJING_ROAD_AREA = {
    "center": {
        "longitude": 121.4825,  # 中心点经度
        "latitude": 31.2390     # 中心点纬度
    },
    # 多边形区域坐标
    "polygon": "121.4750,31.2430|121.4900,31.2430|121.4900,31.2350|121.4750,31.2350|121.4750,31.2430"
}
```

### 搜索参数

```python
DEFAULT_SEARCH_PARAMS = {
    "radius": 1000,        # 搜索半径(米)
    "page_size": 25,       # 每页返回数量
    "extensions": "all",   # 返回详细信息
    "rate_limit": 0.25     # 请求间隔(秒)
}
```

## 数据格式

### POI数据结构

```json
{
    "id": "B0FFFAB6J2",
    "name": "星巴克咖啡",
    "type": "餐饮服务;咖啡厅;星巴克",
    "typecode": "050301",
    "address": "上海市黄浦区南京东路432号",
    "location": "121.486532,31.239812",
    "tel": "021-6321XXXX",
    "rating": "4.6",
    "cost": "45",
    "tag": "咖啡,饮品,轻食"
}
```

## API限制

- **个人认证**: 100次/日
- **企业认证**: 1000次/日
- **QPS限制**: 建议不超过10次/分钟

## 注意事项

1. **坐标系统**: 高德地图使用GCJ-02坐标系
2. **数据合规**: 请遵守高德地图服务协议，标注数据来源
3. **缓存建议**: 对于相同区域的重复查询，建议缓存结果
4. **错误处理**: 代码已包含基本的错误处理和重试机制

## 常见问题

### Q: 如何扩大搜索范围？

A: 有两种方式：
1. 增加 `radius` 参数（周边搜索）
2. 修改 `polygon` 坐标（多边形搜索）

### Q: 如何获取更多POI类型？

A: 在 `search_types` 参数中添加更多类型，或直接使用类型编码。

### Q: API返回数据为空怎么办？

A: 检查以下几点：
1. API Key是否正确
2. 搜索区域是否有对应的POI
3. 类型编码是否正确
4. 是否超出API调用限制

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
