"""
POI获取工具测试脚本
用于验证代码的正确性和API连接
"""
import os
import sys
from poi_fetcher import AmapPOIFetcher
from config import POI_TYPES, NANJING_ROAD_AREA


def test_api_connection():
    """测试API连接"""
    print("🔗 测试API连接...")

    api_key = os.getenv("AMAP_API_KEY")
    if not api_key:
        print("❌ 未设置API Key，跳过连接测试")
        return False

    try:
        fetcher = AmapPOIFetcher(api_key)

        # 简单的搜索测试
        pois = fetcher.search_text("银行", "上海")

        if pois:
            print(f"✅ API连接正常，找到 {len(pois)} 个银行")
            return True
        else:
            print("⚠️  API连接正常，但未找到结果")
            return True

    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False


def test_config():
    """测试配置文件"""
    print("⚙️  测试配置文件...")

    # 检查POI类型配置
    if not POI_TYPES:
        print("❌ POI_TYPES配置为空")
        return False

    print(f"✅ 配置了 {len(POI_TYPES)} 种POI类型")

    # 检查南京东路区域配置
    if not NANJING_ROAD_AREA:
        print("❌ NANJING_ROAD_AREA配置为空")
        return False

    center = NANJING_ROAD_AREA.get("center", {})
    if not center.get("longitude") or not center.get("latitude"):
        print("❌ 南京东路中心点坐标配置错误")
        return False

    print(f"✅ 南京东路中心点: ({center['longitude']}, {center['latitude']})")

    polygon = NANJING_ROAD_AREA.get("polygon", "")
    if not polygon:
        print("❌ 南京东路多边形区域配置为空")
        return False

    # 验证多边形格式
    coords = polygon.split("|")
    if len(coords) < 4:
        print("❌ 多边形坐标点数量不足")
        return False

    print(f"✅ 多边形区域配置正常，包含 {len(coords)} 个坐标点")

    return True


def test_data_normalization():
    """测试数据标准化功能"""
    print("📊 测试数据标准化...")

    # 模拟POI数据
    mock_poi = {
        "id": "B0FFFAB6J2",
        "name": "  测试POI  ",
        "type": "餐饮服务;中餐厅;川菜",
        "typecode": "050118",
        "address": "上海市黄浦区南京东路123号",
        "location": "121.486532,31.239812",
        "tel": "021-12345678",
        "biz_ext": {
            "rating": "4.5",
            "cost": "80"
        }
    }

    try:
        normalized = AmapPOIFetcher.normalize_poi(mock_poi)

        # 检查必要字段
        required_fields = ["id", "name", "type", "address", "location"]
        for field in required_fields:
            if field not in normalized:
                print(f"❌ 缺少必要字段: {field}")
                return False

        # 检查名称是否去除了空格
        if normalized["name"] != "测试POI":
            print(f"❌ 名称标准化失败: {normalized['name']}")
            return False

        # 检查评分和消费信息
        if normalized["rating"] != "4.5":
            print(f"❌ 评分提取失败: {normalized['rating']}")
            return False

        if normalized["cost"] != "80":
            print(f"❌ 消费信息提取失败: {normalized['cost']}")
            return False

        print("✅ 数据标准化功能正常")
        return True

    except Exception as e:
        print(f"❌ 数据标准化测试失败: {e}")
        return False


def test_export_functions():
    """测试导出功能"""
    print("📤 测试导出功能...")

    # 模拟POI数据
    mock_pois = [
        {
            "id": "B0001",
            "name": "测试餐厅1",
            "type": "餐饮服务;中餐厅",
            "address": "上海市黄浦区南京东路100号",
            "location": "121.486,31.239",
            "tel": "021-11111111",
            "rating": "4.5",
            "cost": "80",
            "tag": "川菜,麻辣",
            "typecode": "050118",
            "website": "",
            "email": "",
            "pname": "",
            "cityname": "",
            "adname": "",
            "business_area": "",
            "postcode": "",
            "distance": ""
        },
        {
            "id": "B0002",
            "name": "测试商店1",
            "type": "购物服务;超市",
            "address": "上海市黄浦区南京东路200号",
            "location": "121.487,31.240",
            "tel": "021-22222222",
            "rating": "",
            "cost": "",
            "tag": "",
            "typecode": "060101",
            "website": "",
            "email": "",
            "pname": "",
            "cityname": "",
            "adname": "",
            "business_area": "",
            "postcode": "",
            "distance": ""
        }
    ]

    try:
        fetcher = AmapPOIFetcher("test_key")  # 使用测试key

        # 测试Markdown导出
        md_content = fetcher.export_to_markdown(mock_pois, "test_output.md")
        if "测试餐厅1" not in md_content:
            print("❌ Markdown导出内容错误")
            return False

        # 测试JSON导出
        fetcher.export_to_json(mock_pois, "test_output.json")

        # 测试CSV导出
        fetcher.export_to_csv(mock_pois, "test_output.csv")

        print("✅ 导出功能正常")

        # 清理测试文件
        for filename in ["test_output.md", "test_output.json", "test_output.csv"]:
            if os.path.exists(filename):
                os.remove(filename)

        return True

    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🧪 POI获取工具测试")
    print("=" * 50)

    tests = [
        ("配置文件", test_config),
        ("数据标准化", test_data_normalization),
        ("导出功能", test_export_functions),
        ("API连接", test_api_connection),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试:")
        if test_func():
            passed += 1
        else:
            print(f"   {test_name}测试失败")

    print(f"\n📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置")

    # 给出使用建议
    print("\n💡 使用建议:")
    if not os.getenv("AMAP_API_KEY"):
        print("   1. 设置环境变量 AMAP_API_KEY 或创建 .env 文件")
    print("   2. 运行 python example.py 查看使用示例")
    print("   3. 运行 python poi_cli.py --help 查看命令行工具帮助")


if __name__ == "__main__":
    main()
