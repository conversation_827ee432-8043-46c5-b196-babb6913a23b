"""
高德地图POI获取示例
演示如何使用AmapPOIFetcher获取南京东路附近的POI信息
"""
import os
from poi_fetcher import AmapPOIFetcher
from config import POI_TYPES


def main():
    """主函数"""
    # 检查API Key
    api_key = os.getenv("AMAP_API_KEY")
    if not api_key:
        print("⚠️  请先设置环境变量 AMAP_API_KEY")
        print("   可以创建 .env 文件，内容如下：")
        print("   AMAP_API_KEY=your_api_key_here")
        return
    
    # 创建POI获取器
    fetcher = AmapPOIFetcher(api_key)
    
    print("🗺️  高德地图POI信息获取工具")
    print("=" * 50)
    
    # 示例1: 获取南京东路附近的所有餐饮和购物POI
    print("\n📍 示例1: 获取南京东路附近的餐饮和购物POI")
    pois = fetcher.get_nanjing_road_pois(
        search_types=["餐饮服务", "购物服务"],
        use_polygon=True
    )
    
    if pois:
        print(f"✅ 成功获取 {len(pois)} 个POI")
        
        # 导出为不同格式
        print("\n📄 导出数据...")
        fetcher.export_to_markdown(pois, "example_pois.md")
        fetcher.export_to_json(pois, "example_pois.json")
        fetcher.export_to_csv(pois, "example_pois.csv")
        print("✅ 数据已导出为 Markdown、JSON 和 CSV 格式")
        
        # 显示前5个POI的基本信息
        print("\n🏪 前5个POI信息预览:")
        for i, poi in enumerate(pois[:5], 1):
            print(f"{i}. {poi['name']}")
            print(f"   地址: {poi['address']}")
            print(f"   类型: {poi['type']}")
            if poi['rating']:
                print(f"   评分: {poi['rating']}")
            print()
    
    # 示例2: 搜索特定关键词
    print("\n📍 示例2: 搜索'星巴克'")
    starbucks_pois = fetcher.search_around(
        longitude=121.4825,
        latitude=31.2390,
        radius=2000,
        keywords="星巴克"
    )
    
    if starbucks_pois:
        print(f"✅ 找到 {len(starbucks_pois)} 家星巴克")
        for poi in starbucks_pois:
            normalized = fetcher.normalize_poi(poi)
            print(f"- {normalized['name']} ({normalized['address']})")
    
    # 示例3: 周边搜索银行
    print("\n📍 示例3: 搜索附近的银行")
    bank_pois = fetcher.search_around(
        longitude=121.4825,
        latitude=31.2390,
        radius=1000,
        types=POI_TYPES["金融保险服务"]
    )
    
    if bank_pois:
        print(f"✅ 找到 {len(bank_pois)} 家银行")
        for poi in bank_pois:
            normalized = fetcher.normalize_poi(poi)
            print(f"- {normalized['name']} ({normalized['address']})")
    
    # 示例4: 自定义区域搜索
    print("\n📍 示例4: 自定义多边形区域搜索")
    # 定义一个小范围的多边形区域
    custom_polygon = "121.4800,31.2400|121.4850,31.2400|121.4850,31.2380|121.4800,31.2380|121.4800,31.2400"
    
    custom_pois = fetcher.search_polygon(
        polygon=custom_polygon,
        types=POI_TYPES["餐饮服务"]
    )
    
    if custom_pois:
        print(f"✅ 在自定义区域找到 {len(custom_pois)} 个餐饮POI")
        for poi in custom_pois[:3]:  # 只显示前3个
            normalized = fetcher.normalize_poi(poi)
            print(f"- {normalized['name']} ({normalized['address']})")
    
    print("\n🎉 示例运行完成！")
    print("💡 提示: 可以修改 config.py 中的配置来调整搜索参数")


def show_available_types():
    """显示可用的POI类型"""
    print("\n📋 可用的POI类型:")
    print("=" * 30)
    for name, code in POI_TYPES.items():
        print(f"{name}: {code}")


def interactive_search():
    """交互式搜索"""
    api_key = os.getenv("AMAP_API_KEY")
    if not api_key:
        print("⚠️  请先设置环境变量 AMAP_API_KEY")
        return
    
    fetcher = AmapPOIFetcher(api_key)
    
    print("\n🔍 交互式POI搜索")
    print("=" * 30)
    
    while True:
        print("\n请选择搜索方式:")
        print("1. 关键词搜索")
        print("2. 周边搜索")
        print("3. 南京东路区域搜索")
        print("4. 显示POI类型")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            keywords = input("请输入关键词: ").strip()
            city = input("请输入城市 (默认: 上海): ").strip() or "上海"
            
            pois = fetcher.search_text(keywords, city)
            print(f"\n找到 {len(pois)} 个结果")
            
            for poi in pois[:5]:
                normalized = fetcher.normalize_poi(poi)
                print(f"- {normalized['name']} ({normalized['address']})")
        
        elif choice == "2":
            try:
                lng = float(input("请输入经度: ").strip())
                lat = float(input("请输入纬度: ").strip())
                radius = int(input("请输入搜索半径(米, 默认1000): ").strip() or "1000")
                keywords = input("请输入关键词(可选): ").strip()
                
                pois = fetcher.search_around(lng, lat, radius, keywords)
                print(f"\n找到 {len(pois)} 个结果")
                
                for poi in pois[:5]:
                    normalized = fetcher.normalize_poi(poi)
                    print(f"- {normalized['name']} ({normalized['address']})")
            except ValueError:
                print("❌ 输入格式错误")
        
        elif choice == "3":
            keywords = input("请输入关键词(可选): ").strip()
            pois = fetcher.get_nanjing_road_pois(keywords=keywords)
            print(f"\n找到 {len(pois)} 个结果")
            
            for poi in pois[:5]:
                print(f"- {poi['name']} ({poi['address']})")
        
        elif choice == "4":
            show_available_types()


if __name__ == "__main__":
    # 运行基本示例
    main()
    
    # 如果需要交互式搜索，取消下面的注释
    # interactive_search()
