"""
高德地图POI信息获取工具
支持关键字搜索、周边搜索、多边形搜索等多种方式
"""
import requests
import time
import json
import pandas as pd
from typing import List, Dict, Optional, Union
from config import (
    AMAP_API_KEY, AMAP_BASE_URL, NANJING_ROAD_AREA,
    POI_TYPES, DEFAULT_SEARCH_PARAMS, OUTPUT_CONFIG
)


class AmapPOIFetcher:
    """高德地图POI获取器"""

    def __init__(self, api_key: str = None):
        """
        初始化POI获取器

        Args:
            api_key: 高德地图API Key，如果不提供则从配置文件获取
        """
        self.api_key = api_key or AMAP_API_KEY
        if not self.api_key:
            raise ValueError("请设置高德地图API Key")

        self.base_url = AMAP_BASE_URL
        self.session = requests.Session()
        self.rate_limit = DEFAULT_SEARCH_PARAMS["rate_limit"]

    def _make_request(self, endpoint: str, params: Dict) -> Dict:
        """
        发送API请求

        Args:
            endpoint: API端点
            params: 请求参数

        Returns:
            API响应数据
        """
        url = f"{self.base_url}/{endpoint}"
        params["key"] = self.api_key

        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("status") != "1":
                raise Exception(f"API错误: {data.get('info', '未知错误')}")

            return data
        except requests.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")

    def search_around(self, longitude: float, latitude: float,
                     radius: int = 1000, keywords: str = "",
                     types: str = "", **kwargs) -> List[Dict]:
        """
        周边搜索

        Args:
            longitude: 中心点经度
            latitude: 中心点纬度
            radius: 搜索半径(米)
            keywords: 关键词
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "location": f"{longitude},{latitude}",
            "radius": radius,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if keywords:
            params["keywords"] = keywords
        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("around", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            # 检查是否还有更多数据
            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)  # 避免请求过快

            # 防止无限循环
            if page > 100:
                break

        return all_pois

    def search_polygon(self, polygon: str, keywords: str = "",
                      types: str = "", **kwargs) -> List[Dict]:
        """
        多边形搜索

        Args:
            polygon: 多边形坐标串
            keywords: 关键词
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "polygon": polygon,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if keywords:
            params["keywords"] = keywords
        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("polygon", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            # 检查是否还有更多数据
            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)

            # 防止无限循环
            if page > 100:
                break

        return all_pois

    def search_text(self, keywords: str, city: str = "上海",
                   types: str = "", **kwargs) -> List[Dict]:
        """
        关键字搜索

        Args:
            keywords: 搜索关键词
            city: 城市名称
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "keywords": keywords,
            "city": city,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("text", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)

            if page > 100:
                break

        return all_pois

    def get_poi_detail(self, poi_id: str) -> Dict:
        """
        获取POI详细信息

        Args:
            poi_id: POI ID

        Returns:
            POI详细信息
        """
        params = {"id": poi_id}
        data = self._make_request("detail", params)
        return data.get("pois", [{}])[0]

    @staticmethod
    def normalize_poi(poi: Dict) -> Dict:
        """
        标准化POI数据，优化为知识库格式

        Args:
            poi: 原始POI数据

        Returns:
            标准化后的POI数据，包含详细的地址解析和楼层信息
        """
        biz_ext = poi.get("biz_ext", {})
        indoor_data = poi.get("indoor_data", {})

        # 解析详细地址信息
        address_info = AmapPOIFetcher._parse_address(poi)

        return {
            # 基本标识信息
            "id": poi.get("id", ""),
            "name": poi.get("name", "").strip(),
            "type": poi.get("type", ""),
            "typecode": poi.get("typecode", ""),

            # 详细地址信息（知识库重点）
            "full_address": poi.get("address", ""),
            "street_number": address_info["street_number"],
            "building_name": address_info["building_name"],
            "building_number": address_info["building_number"],
            "floor_info": address_info["floor_info"],
            "room_number": address_info["room_number"],

            # 地理位置信息
            "location": poi.get("location", ""),
            "pname": poi.get("pname", ""),
            "cityname": poi.get("cityname", ""),
            "adname": poi.get("adname", ""),
            "business_area": poi.get("business_area", ""),

            # 联系方式
            "tel": poi.get("tel", ""),
            "website": poi.get("website", ""),
            "email": poi.get("email", ""),

            # 楼层和建筑物信息
            "floor": poi.get("floor", ""),
            "true_floor": poi.get("truefloor", ""),
            "parent_poi_id": poi.get("parent", ""),
            "cpid": poi.get("cpid", ""),
            "indoor_map": poi.get("indoor_map", ""),

            # 商业信息
            "rating": biz_ext.get("rating", ""),
            "cost": biz_ext.get("cost", ""),
            "tag": poi.get("tag", ""),

            # 其他信息
            "postcode": poi.get("postcode", ""),
            "distance": poi.get("distance", ""),
            "entrance_location": poi.get("entr_location", ""),
            "exit_location": poi.get("exit_location", "")
        }

    @staticmethod
    def _parse_address(poi: Dict) -> Dict:
        """
        解析地址信息，提取街道门牌号、大楼名称、楼层等信息

        Args:
            poi: POI原始数据

        Returns:
            解析后的地址信息字典
        """
        import re

        address = poi.get("address", "")
        name = poi.get("name", "")

        # 初始化地址信息
        address_info = {
            "street_number": "",
            "building_name": "",
            "building_number": "",
            "floor_info": "",
            "room_number": ""
        }

        if not address:
            return address_info

        # 提取街道门牌号（如：南京东路432号）
        street_pattern = r'([^市区县]+[路街道巷弄])\s*(\d+[号楼栋]?)'
        street_match = re.search(street_pattern, address)
        if street_match:
            address_info["street_number"] = f"{street_match.group(1)}{street_match.group(2)}"

        # 提取大楼名称（从地址或POI名称中）
        building_patterns = [
            r'([^市区县]+(?:大厦|大楼|广场|中心|商城|商场|写字楼|办公楼|酒店|宾馆|银行|大厦))',
            r'(\d+号[^,，]*(?:大厦|大楼|广场|中心|商城|商场|写字楼|办公楼))',
        ]

        for pattern in building_patterns:
            match = re.search(pattern, address)
            if match:
                address_info["building_name"] = match.group(1)
                break

        # 如果地址中没有找到建筑物名称，尝试从POI名称中提取
        if not address_info["building_name"]:
            name_building_pattern = r'([^-]+(?:大厦|大楼|广场|中心|商城|商场|写字楼|办公楼|酒店|宾馆|银行))'
            name_match = re.search(name_building_pattern, name)
            if name_match:
                address_info["building_name"] = name_match.group(1)

        # 提取楼层信息（从POI的floor字段或名称中）
        floor = poi.get("floor", "")
        true_floor = poi.get("truefloor", "")

        if true_floor:
            address_info["floor_info"] = true_floor
        elif floor:
            address_info["floor_info"] = f"第{floor}层" if floor.isdigit() else floor
        else:
            # 从名称中提取楼层信息
            floor_patterns = [
                r'([B]?\d+[层楼F])',
                r'(地下\d+层)',
                r'(负\d+层)',
                r'(\d+F)',
                r'(B\d+)'
            ]

            for pattern in floor_patterns:
                match = re.search(pattern, name)
                if match:
                    address_info["floor_info"] = match.group(1)
                    break

        # 提取房间号（从POI名称中）
        room_patterns = [
            r'(\d{3,4}[室房号])',
            r'([A-Z]\d{2,3})',
            r'(\d+-\d+)'
        ]

        for pattern in room_patterns:
            match = re.search(pattern, name)
            if match:
                address_info["room_number"] = match.group(1)
                break

        return address_info

    def export_to_markdown(self, pois: List[Dict], filename: str = None) -> str:
        """
        导出POI数据为知识库优化的Markdown格式

        Args:
            pois: POI数据列表
            filename: 输出文件名

        Returns:
            Markdown内容
        """
        if not filename:
            filename = OUTPUT_CONFIG["markdown_file"]

        content = "# 南京东路POI知识库\n\n"
        content += f"**数据更新时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        content += f"**POI总数**: {len(pois)} 个\n\n"
        content += "---\n\n"

        # 按类型分组
        poi_by_type = {}
        for poi in pois:
            poi_type = poi.get('type', '未分类').split(';')[0] if poi.get('type') else '未分类'
            if poi_type not in poi_by_type:
                poi_by_type[poi_type] = []
            poi_by_type[poi_type].append(poi)

        # 生成目录
        content += "## 📋 目录\n\n"
        for poi_type, poi_list in poi_by_type.items():
            content += f"- [{poi_type}](#{poi_type.replace(' ', '-')}) ({len(poi_list)}个)\n"
        content += "\n---\n\n"

        # 按类型输出POI信息
        for poi_type, poi_list in poi_by_type.items():
            content += f"## {poi_type}\n\n"

            for poi in poi_list:
                content += f"### 🏪 {poi['name']}\n\n"

                # 详细地址信息（知识库重点）
                content += "#### 📍 位置信息\n\n"

                if poi.get('full_address'):
                    content += f"**完整地址**: {poi['full_address']}\n\n"

                if poi.get('street_number'):
                    content += f"**街道门牌**: {poi['street_number']}\n\n"

                if poi.get('building_name'):
                    content += f"**建筑物名称**: {poi['building_name']}\n\n"

                if poi.get('floor_info'):
                    content += f"**楼层**: {poi['floor_info']}\n\n"

                if poi.get('room_number'):
                    content += f"**房间号**: {poi['room_number']}\n\n"

                if poi.get('business_area'):
                    content += f"**商圈**: {poi['business_area']}\n\n"

                if poi.get('location'):
                    content += f"**GPS坐标**: {poi['location']}\n\n"

                # 联系方式
                if poi.get('tel') or poi.get('website') or poi.get('email'):
                    content += "#### 📞 联系方式\n\n"

                    if poi.get('tel'):
                        content += f"**电话**: {poi['tel']}\n\n"

                    if poi.get('website'):
                        content += f"**网站**: {poi['website']}\n\n"

                    if poi.get('email'):
                        content += f"**邮箱**: {poi['email']}\n\n"

                # 商业信息
                if poi.get('rating') or poi.get('cost') or poi.get('tag'):
                    content += "#### 💼 商业信息\n\n"

                    if poi.get('type'):
                        content += f"**业务类型**: {poi['type']}\n\n"

                    if poi.get('rating'):
                        content += f"**评分**: ⭐ {poi['rating']}\n\n"

                    if poi.get('cost'):
                        content += f"**人均消费**: ¥{poi['cost']}\n\n"

                    if poi.get('tag'):
                        content += f"**特色标签**: {poi['tag']}\n\n"

                # 建筑物信息
                if poi.get('parent_poi_id') or poi.get('indoor_map') or poi.get('entrance_location'):
                    content += "#### 🏢 建筑物信息\n\n"

                    if poi.get('parent_poi_id'):
                        content += f"**所属建筑物ID**: {poi['parent_poi_id']}\n\n"

                    if poi.get('indoor_map') == "1":
                        content += f"**室内地图**: ✅ 有室内地图\n\n"

                    if poi.get('entrance_location'):
                        content += f"**入口坐标**: {poi['entrance_location']}\n\n"

                # 系统信息
                content += "#### 🔍 系统信息\n\n"
                content += f"**POI ID**: `{poi.get('id', '')}`\n\n"
                content += f"**类型编码**: `{poi.get('typecode', '')}`\n\n"

                content += "---\n\n"

        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return content

    def export_to_json(self, pois: List[Dict], filename: str = None) -> None:
        """
        导出POI数据为JSON格式

        Args:
            pois: POI数据列表
            filename: 输出文件名
        """
        if not filename:
            filename = OUTPUT_CONFIG["json_file"]

        data = {
            "total_count": len(pois),
            "pois": pois
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def export_to_csv(self, pois: List[Dict], filename: str = None) -> None:
        """
        导出POI数据为CSV格式

        Args:
            pois: POI数据列表
            filename: 输出文件名
        """
        if not filename:
            filename = OUTPUT_CONFIG["csv_file"]

        df = pd.DataFrame(pois)
        df.to_csv(filename, index=False, encoding='utf-8-sig')

    def get_nanjing_road_pois(self, search_types: List[str] = None,
                             keywords: str = "", use_polygon: bool = True) -> List[Dict]:
        """
        获取南京东路附近的POI信息

        Args:
            search_types: 搜索的POI类型列表
            keywords: 关键词
            use_polygon: 是否使用多边形搜索

        Returns:
            标准化的POI数据列表
        """
        if search_types is None:
            # 默认搜索常见类型
            search_types = ["餐饮服务", "购物服务", "生活服务", "金融保险服务"]

        # 构建类型编码字符串
        type_codes = []
        for type_name in search_types:
            if type_name in POI_TYPES:
                type_codes.append(POI_TYPES[type_name])

        types_str = "|".join(type_codes) if type_codes else ""

        print(f"开始搜索南京东路POI信息...")
        print(f"搜索类型: {', '.join(search_types)}")
        print(f"关键词: {keywords or '无'}")

        if use_polygon:
            # 使用多边形搜索
            pois = self.search_polygon(
                polygon=NANJING_ROAD_AREA["polygon"],
                keywords=keywords,
                types=types_str
            )
        else:
            # 使用周边搜索
            center = NANJING_ROAD_AREA["center"]
            pois = self.search_around(
                longitude=center["longitude"],
                latitude=center["latitude"],
                radius=DEFAULT_SEARCH_PARAMS["radius"],
                keywords=keywords,
                types=types_str
            )

        print(f"找到 {len(pois)} 个POI")

        # 标准化数据
        normalized_pois = [self.normalize_poi(poi) for poi in pois]

        return normalized_pois

    def export_to_knowledge_base(self, pois: List[Dict], filename: str = None) -> str:
        """
        导出为知识库专用格式，包含结构化的问答对

        Args:
            pois: POI数据列表
            filename: 输出文件名

        Returns:
            知识库内容
        """
        if not filename:
            filename = "knowledge_base.md"

        content = "# 南京东路POI知识库\n\n"
        content += "## 使用说明\n\n"
        content += "本知识库包含南京东路附近的详细POI信息，支持以下查询：\n"
        content += "- 根据商户名称查询详细信息\n"
        content += "- 根据地址查询附近商户\n"
        content += "- 根据业务类型查询相关商户\n"
        content += "- 根据楼层查询同层商户\n\n"
        content += "---\n\n"

        # 为每个POI生成结构化的知识条目
        for poi in pois:
            content += f"## {poi['name']}\n\n"

            # 基本信息摘要
            content += "### 基本信息\n\n"
            content += f"**名称**: {poi['name']}\n\n"

            # 构建完整地址描述
            address_parts = []
            if poi.get('street_number'):
                address_parts.append(poi['street_number'])
            if poi.get('building_name'):
                address_parts.append(poi['building_name'])
            if poi.get('floor_info'):
                address_parts.append(poi['floor_info'])
            if poi.get('room_number'):
                address_parts.append(poi['room_number'])

            if address_parts:
                content += f"**详细位置**: {' '.join(address_parts)}\n\n"
            elif poi.get('full_address'):
                content += f"**地址**: {poi['full_address']}\n\n"

            if poi.get('type'):
                content += f"**业务类型**: {poi['type']}\n\n"

            # 联系信息
            if poi.get('tel'):
                content += f"**联系电话**: {poi['tel']}\n\n"

            # 商业信息
            if poi.get('rating'):
                content += f"**用户评分**: {poi['rating']}分\n\n"

            if poi.get('cost'):
                content += f"**人均消费**: {poi['cost']}元\n\n"

            if poi.get('tag'):
                content += f"**特色服务**: {poi['tag']}\n\n"

            # 生成常见问答
            content += "### 常见问题\n\n"

            # Q1: 位置查询
            content += f"**Q: {poi['name']}在哪里？**\n\n"
            if poi.get('street_number') and poi.get('building_name'):
                content += f"A: {poi['name']}位于{poi['street_number']}{poi['building_name']}"
                if poi.get('floor_info'):
                    content += f"{poi['floor_info']}"
                if poi.get('room_number'):
                    content += f"{poi['room_number']}"
                content += "。\n\n"
            elif poi.get('full_address'):
                content += f"A: {poi['name']}的地址是{poi['full_address']}。\n\n"

            # Q2: 联系方式
            if poi.get('tel'):
                content += f"**Q: {poi['name']}的电话是多少？**\n\n"
                content += f"A: {poi['name']}的联系电话是{poi['tel']}。\n\n"

            # Q3: 业务信息
            if poi.get('type'):
                content += f"**Q: {poi['name']}是做什么的？**\n\n"
                content += f"A: {poi['name']}主要经营{poi['type'].split(';')[-1] if ';' in poi['type'] else poi['type']}"
                if poi.get('tag'):
                    content += f"，特色服务包括{poi['tag']}"
                content += "。\n\n"

            # Q4: 消费信息
            if poi.get('cost'):
                content += f"**Q: {poi['name']}的消费水平如何？**\n\n"
                content += f"A: {poi['name']}的人均消费约为{poi['cost']}元"
                if poi.get('rating'):
                    content += f"，用户评分{poi['rating']}分"
                content += "。\n\n"

            # Q5: 同楼层其他商户（如果有楼层信息）
            if poi.get('floor_info'):
                same_floor_pois = [p for p in pois if p.get('floor_info') == poi.get('floor_info') and p['id'] != poi['id']]
                if same_floor_pois:
                    content += f"**Q: {poi['floor_info']}还有哪些商户？**\n\n"
                    content += f"A: {poi['floor_info']}还有"
                    same_floor_names = [p['name'] for p in same_floor_pois[:5]]  # 最多显示5个
                    content += "、".join(same_floor_names)
                    if len(same_floor_pois) > 5:
                        content += f"等{len(same_floor_pois)}家商户"
                    content += "。\n\n"

            content += "---\n\n"

        # 添加汇总信息
        content += "## 汇总信息\n\n"

        # 按类型统计
        type_stats = {}
        for poi in pois:
            poi_type = poi.get('type', '').split(';')[0] if poi.get('type') else '未分类'
            type_stats[poi_type] = type_stats.get(poi_type, 0) + 1

        content += "### 按业务类型统计\n\n"
        for poi_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            content += f"- {poi_type}: {count}家\n"
        content += "\n"

        # 按楼层统计（如果有楼层信息）
        floor_stats = {}
        for poi in pois:
            if poi.get('floor_info'):
                floor_stats[poi['floor_info']] = floor_stats.get(poi['floor_info'], 0) + 1

        if floor_stats:
            content += "### 按楼层分布统计\n\n"
            for floor, count in sorted(floor_stats.items()):
                content += f"- {floor}: {count}家\n"
            content += "\n"

        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return content
