"""
高德地图POI信息获取工具
支持关键字搜索、周边搜索、多边形搜索等多种方式
"""
import requests
import time
import json
import pandas as pd
from typing import List, Dict, Optional, Union
from config import (
    AMAP_API_KEY, AMAP_BASE_URL, NANJING_ROAD_AREA,
    POI_TYPES, DEFAULT_SEARCH_PARAMS, OUTPUT_CONFIG
)


class AmapPOIFetcher:
    """高德地图POI获取器"""

    def __init__(self, api_key: str = None):
        """
        初始化POI获取器

        Args:
            api_key: 高德地图API Key，如果不提供则从配置文件获取
        """
        self.api_key = api_key or AMAP_API_KEY
        if not self.api_key:
            raise ValueError("请设置高德地图API Key")

        self.base_url = AMAP_BASE_URL
        self.session = requests.Session()
        self.rate_limit = DEFAULT_SEARCH_PARAMS["rate_limit"]

    def _make_request(self, endpoint: str, params: Dict) -> Dict:
        """
        发送API请求

        Args:
            endpoint: API端点
            params: 请求参数

        Returns:
            API响应数据
        """
        url = f"{self.base_url}/{endpoint}"
        params["key"] = self.api_key

        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("status") != "1":
                raise Exception(f"API错误: {data.get('info', '未知错误')}")

            return data
        except requests.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")

    def search_around(self, longitude: float, latitude: float,
                     radius: int = 1000, keywords: str = "",
                     types: str = "", **kwargs) -> List[Dict]:
        """
        周边搜索

        Args:
            longitude: 中心点经度
            latitude: 中心点纬度
            radius: 搜索半径(米)
            keywords: 关键词
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "location": f"{longitude},{latitude}",
            "radius": radius,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if keywords:
            params["keywords"] = keywords
        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("around", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            # 检查是否还有更多数据
            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)  # 避免请求过快

            # 防止无限循环
            if page > 100:
                break

        return all_pois

    def search_polygon(self, polygon: str, keywords: str = "",
                      types: str = "", **kwargs) -> List[Dict]:
        """
        多边形搜索

        Args:
            polygon: 多边形坐标串
            keywords: 关键词
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "polygon": polygon,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if keywords:
            params["keywords"] = keywords
        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("polygon", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            # 检查是否还有更多数据
            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)

            # 防止无限循环
            if page > 100:
                break

        return all_pois

    def search_text(self, keywords: str, city: str = "上海",
                   types: str = "", **kwargs) -> List[Dict]:
        """
        关键字搜索

        Args:
            keywords: 搜索关键词
            city: 城市名称
            types: POI类型编码
            **kwargs: 其他参数

        Returns:
            POI列表
        """
        params = {
            "keywords": keywords,
            "city": city,
            "offset": kwargs.get("page_size", DEFAULT_SEARCH_PARAMS["page_size"]),
            "extensions": kwargs.get("extensions", DEFAULT_SEARCH_PARAMS["extensions"])
        }

        if types:
            params["types"] = types

        all_pois = []
        page = 1

        while True:
            params["page"] = page
            data = self._make_request("text", params)

            pois = data.get("pois", [])
            if not pois:
                break

            all_pois.extend(pois)

            if len(pois) < params["offset"]:
                break

            page += 1
            time.sleep(self.rate_limit)

            if page > 100:
                break

        return all_pois

    def get_poi_detail(self, poi_id: str) -> Dict:
        """
        获取POI详细信息

        Args:
            poi_id: POI ID

        Returns:
            POI详细信息
        """
        params = {"id": poi_id}
        data = self._make_request("detail", params)
        return data.get("pois", [{}])[0]

    @staticmethod
    def normalize_poi(poi: Dict) -> Dict:
        """
        标准化POI数据

        Args:
            poi: 原始POI数据

        Returns:
            标准化后的POI数据
        """
        biz_ext = poi.get("biz_ext", {})

        return {
            "id": poi.get("id", ""),
            "name": poi.get("name", "").strip(),
            "type": poi.get("type", ""),
            "typecode": poi.get("typecode", ""),
            "address": poi.get("address", ""),
            "location": poi.get("location", ""),
            "tel": poi.get("tel", ""),
            "postcode": poi.get("postcode", ""),
            "website": poi.get("website", ""),
            "email": poi.get("email", ""),
            "pname": poi.get("pname", ""),
            "cityname": poi.get("cityname", ""),
            "adname": poi.get("adname", ""),
            "business_area": poi.get("business_area", ""),
            "rating": biz_ext.get("rating", ""),
            "cost": biz_ext.get("cost", ""),
            "tag": poi.get("tag", ""),
            "distance": poi.get("distance", "")
        }

    def export_to_markdown(self, pois: List[Dict], filename: str = None) -> str:
        """
        导出POI数据为Markdown格式

        Args:
            pois: POI数据列表
            filename: 输出文件名

        Returns:
            Markdown内容
        """
        if not filename:
            filename = OUTPUT_CONFIG["markdown_file"]

        content = "# 南京东路POI信息\n\n"
        content += f"总计找到 {len(pois)} 个POI点\n\n"

        for poi in pois:
            content += f"## {poi['name']}\n\n"

            # 基本信息
            if poi['address']:
                content += f"**地址**: {poi['address']}\n\n"
            if poi['tel']:
                content += f"**电话**: {poi['tel']}\n\n"
            if poi['type']:
                content += f"**类型**: {poi['type']}\n\n"
            if poi['location']:
                content += f"**坐标**: {poi['location']}\n\n"
            if poi['rating']:
                content += f"**评分**: {poi['rating']}\n\n"
            if poi['cost']:
                content += f"**人均消费**: {poi['cost']}元\n\n"
            if poi['tag']:
                content += f"**标签**: {poi['tag']}\n\n"
            if poi['website']:
                content += f"**网站**: {poi['website']}\n\n"

            content += "---\n\n"

        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return content

    def export_to_json(self, pois: List[Dict], filename: str = None) -> None:
        """
        导出POI数据为JSON格式

        Args:
            pois: POI数据列表
            filename: 输出文件名
        """
        if not filename:
            filename = OUTPUT_CONFIG["json_file"]

        data = {
            "total_count": len(pois),
            "pois": pois
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def export_to_csv(self, pois: List[Dict], filename: str = None) -> None:
        """
        导出POI数据为CSV格式

        Args:
            pois: POI数据列表
            filename: 输出文件名
        """
        if not filename:
            filename = OUTPUT_CONFIG["csv_file"]

        df = pd.DataFrame(pois)
        df.to_csv(filename, index=False, encoding='utf-8-sig')

    def get_nanjing_road_pois(self, search_types: List[str] = None,
                             keywords: str = "", use_polygon: bool = True) -> List[Dict]:
        """
        获取南京东路附近的POI信息

        Args:
            search_types: 搜索的POI类型列表
            keywords: 关键词
            use_polygon: 是否使用多边形搜索

        Returns:
            标准化的POI数据列表
        """
        if search_types is None:
            # 默认搜索常见类型
            search_types = ["餐饮服务", "购物服务", "生活服务", "金融保险服务"]

        # 构建类型编码字符串
        type_codes = []
        for type_name in search_types:
            if type_name in POI_TYPES:
                type_codes.append(POI_TYPES[type_name])

        types_str = "|".join(type_codes) if type_codes else ""

        print(f"开始搜索南京东路POI信息...")
        print(f"搜索类型: {', '.join(search_types)}")
        print(f"关键词: {keywords or '无'}")

        if use_polygon:
            # 使用多边形搜索
            pois = self.search_polygon(
                polygon=NANJING_ROAD_AREA["polygon"],
                keywords=keywords,
                types=types_str
            )
        else:
            # 使用周边搜索
            center = NANJING_ROAD_AREA["center"]
            pois = self.search_around(
                longitude=center["longitude"],
                latitude=center["latitude"],
                radius=DEFAULT_SEARCH_PARAMS["radius"],
                keywords=keywords,
                types=types_str
            )

        print(f"找到 {len(pois)} 个POI")

        # 标准化数据
        normalized_pois = [self.normalize_poi(poi) for poi in pois]

        return normalized_pois
